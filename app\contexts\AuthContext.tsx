"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase, signInWithEmail, signUpWithEmail, resetPassword, signOut, getUserProfile, upsertUserProfile } from '../lib/supabase'
import type { User } from '../types'
import type { User as SupabaseUser } from '@supabase/supabase-js'

// Authentication types
export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  confirmPassword: string
  name: string
  phone?: string
  agreeToTerms: boolean
  agreeToMarketing?: boolean
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export type AuthModalType = 'login' | 'register' | 'forgot-password' | null

export interface AuthContextType {
  // State
  authState: AuthState
  currentModal: AuthModalType
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  
  // Modal controls
  openModal: (type: AuthModalType) => void
  closeModal: () => void
  
  // Utility functions
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [currentModal, setCurrentModal] = useState<AuthModalType>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Auth state
  const authState: AuthState = {
    user: currentUser,
    isLoading,
    isAuthenticated: !!currentUser,
    error
  }

  // Clear error function
  const clearError = () => setError(null)

  // Initialize auth state and listen for auth changes
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        await loadUserProfile(session.user)
      }
      setIsLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        await loadUserProfile(session.user)
      } else if (event === 'SIGNED_OUT') {
        setCurrentUser(null)
      }
      setIsLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  // Load user profile from database
  const loadUserProfile = async (authUser: SupabaseUser) => {
    try {
      const profile = await getUserProfile(authUser.id)
      if (profile) {
        const user: User = {
          id: authUser.id,
          email: authUser.email || '',
          name: profile.name || authUser.user_metadata?.name || '',
          role: profile.role || 'user',
          walletBalance: profile.wallet_balance || 0,
          avatar: profile.avatar || authUser.user_metadata?.avatar_url
        }
        setCurrentUser(user)
      } else {
        // Create profile if it doesn't exist
        const name = authUser.user_metadata?.name || authUser.email?.split('@')[0] || 'User'
        await upsertUserProfile(authUser.id, {
          name,
          role: 'user',
          wallet_balance: 0
        })
        const user: User = {
          id: authUser.id,
          email: authUser.email || '',
          name,
          role: 'user',
          walletBalance: 0
        }
        setCurrentUser(user)
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
      setError('خطأ في تحميل بيانات المستخدم')
    }
  }

  // Login function
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const result = await signInWithEmail(credentials.email, credentials.password)

      if (!result.success) {
        throw new Error(result.error || 'البريد الإلكتروني أو كلمة المرور غير صحيحة')
      }

      // User profile will be loaded automatically by the auth state change listener
      setCurrentModal(null)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدخول'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Register function
  const register = async (data: RegisterData): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      // Validate passwords match
      if (data.password !== data.confirmPassword) {
        throw new Error('كلمات المرور غير متطابقة')
      }

      const result = await signUpWithEmail(data.email, data.password, data.name)

      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ أثناء إنشاء الحساب')
      }

      // User profile will be created automatically by the signup function
      setCurrentModal(null)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء الحساب'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true)

      const result = await signOut()

      if (!result.success) {
        console.error('Logout error:', result.error)
      }

      // User state will be cleared automatically by the auth state change listener

    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Reset password function
  const resetPasswordFunction = async (email: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const result = await resetPassword(email)

      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور')
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Modal controls
  const openModal = (type: AuthModalType) => {
    clearError()
    setCurrentModal(type)
  }

  const closeModal = () => {
    clearError()
    setCurrentModal(null)
  }

  const value: AuthContextType = {
    authState,
    currentModal,
    login,
    register,
    logout,
    resetPassword: resetPasswordFunction,
    openModal,
    closeModal,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
