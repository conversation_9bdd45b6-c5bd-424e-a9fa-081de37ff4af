"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { mockProducts, mockUsers, mockOrders, mockBanners, mockHomepageSections } from "../data/mockData"
import type { Product, User, Order, BannerSlide, HomepageSection } from "../types"
import { validateData, sanitizeObject } from "../lib/validations"
import { productSchema, userSchema, orderSchema, bannerSlideSchema, homepageSectionSchema } from "../lib/validations"

interface DataContextType {
  // Products
  products: Product[]
  setProducts: React.Dispatch<React.SetStateAction<Product[]>>
  updateProduct: (product: Product) => Promise<{ success: boolean; error?: string }>
  deleteProduct: (productId: string) => Promise<{ success: boolean; error?: string }>
  addProduct: (product: Product) => Promise<{ success: boolean; error?: string }>

  // Users
  users: User[]
  setUsers: React.Dispatch<React.SetStateAction<User[]>>
  updateUser: (user: User) => Promise<{ success: boolean; error?: string }>
  deleteUser: (userId: string) => Promise<{ success: boolean; error?: string }>
  addUser: (user: User) => Promise<{ success: boolean; error?: string }>
  currentUser: User | null
  setCurrentUser: React.Dispatch<React.SetStateAction<User | null>>

  // Orders
  orders: Order[]
  setOrders: React.Dispatch<React.SetStateAction<Order[]>>
  updateOrder: (order: Order) => Promise<{ success: boolean; error?: string }>
  deleteOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>
  addOrder: (order: Order) => Promise<{ success: boolean; error?: string }>

  // Homepage Configuration
  banners: BannerSlide[]
  setBanners: React.Dispatch<React.SetStateAction<BannerSlide[]>>
  updateBanner: (banner: BannerSlide) => Promise<{ success: boolean; error?: string }>
  deleteBanner: (bannerId: string) => Promise<{ success: boolean; error?: string }>
  addBanner: (banner: BannerSlide) => Promise<{ success: boolean; error?: string }>

  homepageSections: HomepageSection[]
  setHomepageSections: React.Dispatch<React.SetStateAction<HomepageSection[]>>
  updateHomepageSection: (section: HomepageSection) => Promise<{ success: boolean; error?: string }>
  deleteHomepageSection: (sectionId: string) => Promise<{ success: boolean; error?: string }>
  addHomepageSection: (section: HomepageSection) => Promise<{ success: boolean; error?: string }>

  // Utility functions
  refreshData: () => Promise<void>
  isLoading: boolean
  error: string | null
  clearError: () => void
}

const DataContext = createContext<DataContextType | undefined>(undefined)

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error("useData must be used within a DataProvider")
  }
  return context
}

interface DataProviderProps {
  children: ReactNode
}

export function DataProvider({ children }: DataProviderProps) {
  // State management for all data
  const [products, setProducts] = useState<Product[]>(mockProducts)
  const [users, setUsers] = useState<User[]>(mockUsers)
  const [orders, setOrders] = useState<Order[]>(mockOrders)
  const [banners, setBanners] = useState<BannerSlide[]>(mockBanners)
  const [homepageSections, setHomepageSections] = useState<HomepageSection[]>(mockHomepageSections)
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize current user (mock admin user for now)
  useEffect(() => {
    // TODO: Replace with actual Supabase authentication
    const adminUser = users.find(user => user.role === "admin")
    if (adminUser) {
      setCurrentUser(adminUser)
    }
  }, [users])

  // Utility functions
  const clearError = () => setError(null)

  const handleError = (error: unknown, operation: string): string => {
    const errorMessage = error instanceof Error ? error.message : `خطأ في ${operation}`
    setError(errorMessage)
    console.error(`Error in ${operation}:`, error)
    return errorMessage
  }

  // Product management functions with validation
  const updateProduct = async (updatedProduct: Product): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      // Sanitize input data
      const sanitizedProduct = sanitizeObject(updatedProduct)

      // Validate product data
      const validation = validateData(productSchema, sanitizedProduct)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setProducts(prev => prev.map(product =>
        product.id === updatedProduct.id ? validation.data as Product : product
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث المنتج')
      return { success: false, error: errorMsg }
    }
  }

  const deleteProduct = async (productId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!productId) {
        const errorMsg = 'معرف المنتج مطلوب'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setProducts(prev => prev.filter(product => product.id !== productId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف المنتج')
      return { success: false, error: errorMsg }
    }
  }

  const addProduct = async (newProduct: Product): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      // Sanitize input data
      const sanitizedProduct = sanitizeObject(newProduct)

      // Validate product data
      const validation = validateData(productSchema, sanitizedProduct)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Check for duplicate slug
      const existingProduct = products.find(p => p.slug === validation.data.slug)
      if (existingProduct) {
        const errorMsg = 'الرابط المختصر موجود بالفعل'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setProducts(prev => [...prev, validation.data as Product])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة المنتج')
      return { success: false, error: errorMsg }
    }
  }

  // User management functions with validation
  const updateUser = async (updatedUser: User): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      // Sanitize input data
      const sanitizedUser = sanitizeObject(updatedUser)

      // Validate user data
      const validation = validateData(userSchema, sanitizedUser)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setUsers(prev => prev.map(user =>
        user.id === updatedUser.id ? validation.data as User : user
      ))

      // Update current user if it's the same user
      if (currentUser && currentUser.id === updatedUser.id) {
        setCurrentUser(validation.data as User)
      }

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث المستخدم')
      return { success: false, error: errorMsg }
    }
  }

  const deleteUser = async (userId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!userId) {
        const errorMsg = 'معرف المستخدم مطلوب'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Prevent deleting current user
      if (currentUser && currentUser.id === userId) {
        const errorMsg = 'لا يمكن حذف المستخدم الحالي'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setUsers(prev => prev.filter(user => user.id !== userId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف المستخدم')
      return { success: false, error: errorMsg }
    }
  }

  const addUser = async (newUser: User): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      // Sanitize input data
      const sanitizedUser = sanitizeObject(newUser)

      // Validate user data
      const validation = validateData(userSchema, sanitizedUser)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Check for duplicate email
      const existingUser = users.find(u => u.email === validation.data.email)
      if (existingUser) {
        const errorMsg = 'البريد الإلكتروني موجود بالفعل'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setUsers(prev => [...prev, validation.data as User])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة المستخدم')
      return { success: false, error: errorMsg }
    }
  }

  // Order management functions
  const updateOrder = async (updatedOrder: Order): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setOrders(prev => prev.map(order =>
        order.id === updatedOrder.id ? updatedOrder : order
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث الطلب')
      return { success: false, error: errorMsg }
    }
  }

  const deleteOrder = async (orderId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setOrders(prev => prev.filter(order => order.id !== orderId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف الطلب')
      return { success: false, error: errorMsg }
    }
  }

  const addOrder = async (newOrder: Order): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setOrders(prev => [...prev, newOrder])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة الطلب')
      return { success: false, error: errorMsg }
    }
  }

  // Banner management functions
  const updateBanner = async (updatedBanner: BannerSlide): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setBanners(prev => prev.map(banner =>
        banner.id === updatedBanner.id ? updatedBanner : banner
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث البانر')
      return { success: false, error: errorMsg }
    }
  }

  const deleteBanner = async (bannerId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setBanners(prev => prev.filter(banner => banner.id !== bannerId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف البانر')
      return { success: false, error: errorMsg }
    }
  }

  const addBanner = async (newBanner: BannerSlide): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setBanners(prev => [...prev, newBanner])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة البانر')
      return { success: false, error: errorMsg }
    }
  }

  // Homepage section management functions
  const updateHomepageSection = async (updatedSection: HomepageSection): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setHomepageSections(prev => prev.map(section =>
        section.id === updatedSection.id ? updatedSection : section
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث القسم')
      return { success: false, error: errorMsg }
    }
  }

  const deleteHomepageSection = async (sectionId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setHomepageSections(prev => prev.filter(section => section.id !== sectionId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف القسم')
      return { success: false, error: errorMsg }
    }
  }

  const addHomepageSection = async (newSection: HomepageSection): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setHomepageSections(prev => [...prev, newSection])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة القسم')
      return { success: false, error: errorMsg }
    }
  }

  // Utility function to refresh all data
  const refreshData = async (): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      // TODO: Implement actual data fetching from Supabase
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay

      setProducts(mockProducts)
      setUsers(mockUsers)
      setOrders(mockOrders)
      setBanners(mockBanners)
      setHomepageSections(mockHomepageSections)
    } catch (error) {
      handleError(error, 'تحديث البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  const value: DataContextType = {
    // Products
    products,
    setProducts,
    updateProduct,
    deleteProduct,
    addProduct,

    // Users
    users,
    setUsers,
    updateUser,
    deleteUser,
    addUser,
    currentUser,
    setCurrentUser,

    // Orders
    orders,
    setOrders,
    updateOrder,
    deleteOrder,
    addOrder,

    // Homepage Configuration
    banners,
    setBanners,
    updateBanner,
    deleteBanner,
    addBanner,

    homepageSections,
    setHomepageSections,
    updateHomepageSection,
    deleteHomepageSection,
    addHomepageSection,

    // Utility
    refreshData,
    isLoading,
    error,
    clearError,
  }

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  )
}
