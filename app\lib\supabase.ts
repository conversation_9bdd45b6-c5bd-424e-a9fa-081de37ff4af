import { createClient } from '@supabase/supabase-js'
import { config } from './config'

// Create a single supabase client for interacting with your database
export const supabase = createClient(
  config.supabase.url,
  config.supabase.anonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  }
)

// Types for Supabase Auth
export interface AuthUser {
  id: string
  email: string
  user_metadata: {
    name?: string
    avatar_url?: string
  }
  app_metadata: {
    role?: 'admin' | 'distributor' | 'user'
  }
}

// Helper function to get user profile data
export async function getUserProfile(userId: string) {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
  
  return data
}

// Helper function to create or update user profile
export async function upsertUserProfile(userId: string, profileData: {
  name: string
  role?: 'admin' | 'distributor' | 'user'
  wallet_balance?: number
  avatar?: string
}) {
  const { data, error } = await supabase
    .from('user_profiles')
    .upsert({
      id: userId,
      ...profileData,
      updated_at: new Date().toISOString()
    })
    .select()
    .single()
  
  if (error) {
    console.error('Error upserting user profile:', error)
    return { success: false, error: error.message }
  }
  
  return { success: true, data }
}

// Helper function to check if user is admin
export async function isUserAdmin(userId: string): Promise<boolean> {
  const profile = await getUserProfile(userId)
  return profile?.role === 'admin'
}

// Helper function to sign out
export async function signOut() {
  const { error } = await supabase.auth.signOut()
  if (error) {
    console.error('Error signing out:', error)
    return { success: false, error: error.message }
  }
  return { success: true }
}

// Helper function to sign in with email and password
export async function signInWithEmail(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  
  if (error) {
    console.error('Error signing in:', error)
    return { success: false, error: error.message }
  }
  
  return { success: true, data }
}

// Helper function to sign up with email and password
export async function signUpWithEmail(email: string, password: string, name: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        name: name
      }
    }
  })
  
  if (error) {
    console.error('Error signing up:', error)
    return { success: false, error: error.message }
  }
  
  // Create user profile after successful signup
  if (data.user) {
    await upsertUserProfile(data.user.id, {
      name: name,
      role: 'user',
      wallet_balance: 0
    })
  }
  
  return { success: true, data }
}

// Helper function to reset password
export async function resetPassword(email: string) {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${config.app.url}/auth/reset-password`
  })
  
  if (error) {
    console.error('Error resetting password:', error)
    return { success: false, error: error.message }
  }
  
  return { success: true }
}

// Helper function to update password
export async function updatePassword(newPassword: string) {
  const { error } = await supabase.auth.updateUser({
    password: newPassword
  })
  
  if (error) {
    console.error('Error updating password:', error)
    return { success: false, error: error.message }
  }
  
  return { success: true }
}

export default supabase
