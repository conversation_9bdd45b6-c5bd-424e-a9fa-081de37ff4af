"use client"

import Link from "next/link"
import Image from "next/image"
import { useState, useEffect } from "react"
import { User, Menu, X, Wallet, Shield, ChevronDown, Search, Settings, LogOut } from "lucide-react"
import MobileSidebar from "./MobileSidebar"
import { useData } from "../contexts/DataContext"
import { useAuth } from "../contexts/AuthContext"
import { preloadAdminComponents } from "./LazyComponents"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [showUserMenu, setShowUserMenu] = useState(false)

  // Use auth context for user data
  const { authState, openModal, logout } = useAuth()

  // Use authenticated user
  const user = authState.user

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !(event.target as Element).closest('.mobile-menu-container')) {
        setIsMenuOpen(false)
      }
      if (showUserMenu && !(event.target as Element).closest('.user-menu-container')) {
        setShowUserMenu(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isMenuOpen, showUserMenu])

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout()
      setShowUserMenu(false)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Navigation items - conditionally include admin dashboard for admin users
  const navItems = [
    { href: "/", label: "الرئيسية", icon: null },
    { href: "/shop", label: "المتجر", icon: null },
    { href: "/wallet", label: "المحفظة", icon: null },
    { href: "/profile", label: "الملف الشخصي", icon: null },
    // Show admin dashboard only for admin users
    ...(user?.role === "admin" ? [{ href: "/admin", label: "لوحة التحكم", icon: Settings }] : []),
  ]

  return (
    <>
      {/* Mobile Sidebar */}
      <MobileSidebar isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      <header
        className="bg-gray-900/95 backdrop-blur-md border-b border-gray-800/50 sticky top-0 z-30"
        role="banner"
      >
        {/* Desktop Header */}
        <div className="hidden lg:block">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Logo */}
              <Link
                href="/"
                className="flex items-center space-x-3 space-x-reverse group"
                aria-label="بنتاكون - الصفحة الرئيسية"
              >
                <div className="w-8 h-8 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow duration-200">
                  <Image
                    src="/logo.jpg"
                    alt="بنتاكون"
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-xl font-bold text-white">بنتاكون</span>
              </Link>

              {/* Navigation Links */}
              <nav className="hidden xl:flex items-center space-x-8 space-x-reverse">
                {navItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="flex items-center space-x-2 space-x-reverse text-gray-300 hover:text-white transition-colors duration-200 group"
                      onMouseEnter={() => {
                        // Preload admin components when hovering over admin link
                        if (item.href === '/admin') {
                          preloadAdminComponents()
                        }
                      }}
                    >
                      {Icon && <Icon className="w-4 h-4 group-hover:text-purple-400 transition-colors duration-200" />}
                      <span className="text-sm font-medium">{item.label}</span>
                    </Link>
                  )
                })}
              </nav>

              {/* Search Bar - Center */}
              <div className="flex-1 max-w-md mx-8">
                <div className="relative w-full">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="ابحث عن الألعاب أو المنتجات"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
                  />
                </div>
              </div>

              {/* Right Side - User Area */}
              <div className="flex items-center">
                {user ? (
                  /* Authenticated User Menu */
                  <div className="relative user-menu-container">
                    <button
                      onClick={() => setShowUserMenu(!showUserMenu)}
                      className="flex items-center space-x-3 space-x-reverse p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
                    >
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                        <User className="w-4 h-4 text-white" />
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-white">{user?.name}</p>
                        <p className="text-xs text-gray-400">{user?.walletBalance || 0} ر.س</p>
                      </div>
                      <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${showUserMenu ? 'rotate-180' : ''}`} />
                    </button>

                    {/* User Dropdown Menu */}
                    {showUserMenu && (
                      <div className="absolute left-0 top-full mt-2 w-48 bg-gray-800/95 backdrop-blur-md border border-gray-700/50 rounded-lg shadow-xl z-50 animate-slide-down">
                        <div className="p-2">
                          <Link
                            href="/profile"
                            className="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200 text-gray-200 hover:text-white"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <User className="w-4 h-4" />
                            <span className="text-sm">الملف الشخصي</span>
                          </Link>
                          <Link
                            href="/wallet"
                            className="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200 text-gray-200 hover:text-white"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <Wallet className="w-4 h-4" />
                            <span className="text-sm">المحفظة</span>
                          </Link>
                          {user?.role === "admin" && (
                            <Link
                              href="/admin"
                              className="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200 text-gray-200 hover:text-white"
                              onClick={() => setShowUserMenu(false)}
                            >
                              <Settings className="w-4 h-4" />
                              <span className="text-sm">لوحة التحكم</span>
                            </Link>
                          )}
                          <hr className="my-2 border-gray-700/50" />
                          <button
                            onClick={handleLogout}
                            className="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-red-500/10 transition-colors duration-200 text-gray-200 hover:text-red-400 w-full text-right"
                          >
                            <LogOut className="w-4 h-4" />
                            <span className="text-sm">تسجيل الخروج</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  /* Login Button for Guests */
                  <button
                    onClick={() => openModal('login')}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg"
                  >
                    تسجيل الدخول
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Header */}
        <div className="lg:hidden">
          <div className="px-4 py-2 space-y-2">
            {/* Top Row: Logo and Login Button */}
            <div className="flex items-center justify-between">
              <Link
                href="/"
                className="flex items-center space-x-2 space-x-reverse"
                aria-label="بنتاكون - الصفحة الرئيسية"
              >
                <div className="w-8 h-8 rounded-lg overflow-hidden shadow-md">
                  <Image
                    src="/logo.jpg"
                    alt="بنتاكون"
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-lg font-bold text-white">بنتاكون</span>
              </Link>

              {/* User Area - Mobile */}
              {user ? (
                <Link
                  href="/profile"
                  className="flex items-center space-x-2 space-x-reverse bg-gray-800/50 hover:bg-gray-700/50 text-white py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200"
                >
                  <div className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                    <User className="w-3 h-3 text-white" />
                  </div>
                  <span>{user?.name}</span>
                </Link>
              ) : (
                <button
                  onClick={() => openModal('login')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg"
                >
                  تسجيل الدخول
                </button>
              )}
            </div>

            {/* Second Row: Menu Button and Search Bar */}
            <div className="flex items-center space-x-3 space-x-reverse">
              {/* Menu Button */}
              <button
                className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex-shrink-0"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                aria-label={isMenuOpen ? "إغلاق القائمة" : "فتح القائمة"}
              >
                <Menu className="w-5 h-5 text-gray-300" />
              </button>

              {/* Search Bar */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="ابحث عن الألعاب أو المنتجات"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

    </>
  )
}
